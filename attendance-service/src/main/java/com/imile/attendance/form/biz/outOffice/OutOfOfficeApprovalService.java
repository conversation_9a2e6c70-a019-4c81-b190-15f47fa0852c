package com.imile.attendance.form.biz.outOffice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.enums.ApprovalNoPrefixEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.approval.OutOfOfficeCustomFieldEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.outOffice.param.OutOfOfficeAddParam;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.ClashApplicationInfoDTO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.bpm.mq.dto.FileTemplateApiDTO;
import com.imile.common.exception.BusinessException;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/15
 * @Description 外勤审批服务
 */
@Slf4j
@Service
public class OutOfOfficeApprovalService {

    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private RpcBpmApprovalClient bpmApprovalClient;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceFormManage formManage;
    @Resource
    private IdWorkUtils idWorkUtils;
    @Resource
    private MigrationService migrationService;

    /**
     * 外勤申请(点击新增按钮调用 新增/暂存)
     *
     * @param param
     * @return
     */
    public ApprovalResultVO outOfOfficeAdd(OutOfOfficeAddParam param) {
        log.info("outOfOfficeAdd | OutOfOfficeAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1. 封装单据参数
        commonFormOperationService.userBaseInfoBuild(null, param, null);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(param.getUserId())) {
            log.info("outOfOfficeAdd | userInfo is on old attendance, userCode:{}" + param.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        // 2. mex特殊校验
        commonFormOperationService.checkMexUser(param.getUserId(),
                ErrorCodeEnum.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK,
                EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO formDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> attendanceFormAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> attendanceFormRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 外勤不需要操作假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        // 3. 保存逻辑及业务校验
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
            outOfOfficeAddDataCheck(param);
            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        // 4. 构建表单信息
        //注意，不仅需要构建审批表信息，正常考勤表也需要落库，状态为未生效,注意假期比例也是为空，只有审批通过，才会扣除假期
        this.outOfOfficeDataAddBuild(param, formDO,
                attendanceFormRelationDOList, attendanceFormAttrDOList,
                employeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        // 5. 暂存逻辑 暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //暂存不用落正常考勤表
            attendanceApprovalManage.formAdd(formDO, attendanceFormRelationDOList, attendanceFormAttrDOList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        // 6. bpm审批流构建
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.outOfOfficeAddApprovalDataBuild(initInfoApiDTO, formDO, attendanceFormAttrDOList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 7. 落库
        attendanceApprovalManage.formAdd(formDO, attendanceFormRelationDOList, attendanceFormAttrDOList, employeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 外勤申请(暂存后更新/驳回后更新)
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ApprovalResultVO outOfOfficeUpdate(OutOfOfficeAddParam param) {
        log.info("outOfOfficeUpdate | OutOfOfficeAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1. 封装单据参数
        commonFormOperationService.userBaseInfoBuild(null, param, null);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(param.getUserId())) {
            log.info("outOfOfficeUpdate | userInfo is on old attendance, userCode:{}" + param.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        // 2. mex特殊校验
        commonFormOperationService.checkMexUser(param.getUserId(),
                ErrorCodeEnum.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK,
                EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //暂存不校验必填项，提交时校验，暂存后进入单据管理-状态为暂存待办
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO EmployeeAbnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        // 外勤不需要操作假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        // 3. 保存逻辑及业务校验
        if (param.getOperationType() == 1) {
            abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
            outOfOfficeAddDataCheck(param);
            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        // 4. 构建表单信息
        outOfOfficeDataUpdateBuild(param, formDO, formRelationDOList, formAttrDOList, EmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);
        // 5. 暂存逻辑 暂存不需要调用bpm
        if (param.getOperationType() == 0) {
            //落库,需要删除所有旧的属性表/关联表数据   主表不能删除在新增，要保持单号的一致，主表更新
            commonFormOperationService.formUpdate(formDO, formDetailBO, attendanceApprovalManage);
            attendanceApprovalManage.formAdd(null, formRelationDOList, formAttrDOList, null, null, userLeaveStageDetailList, null);
            return resultVO;
        }
        // 6. bpm审批流构建
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.outOfOfficeAddApprovalDataBuild(initInfoApiDTO, formDO, formAttrDOList);
        // 7. 落库 本次保存是否是驳回后重提
        commonFormOperationService.updateFormBuild(formDO, formDetailBO, initInfoApiDTO, resultVO);
        attendanceApprovalManage.formAdd(null, formRelationDOList, formAttrDOList, EmployeeAbnormalOperationRecordDO, abnormalAttendanceDO, userLeaveStageDetailList, null);
        return resultVO;
    }

    /**
     * 外勤-撤销申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO outOfOfficeRevokeAdd(RevokeAddParam param) {
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        AttendanceFormDetailBO formDetailBO = null;
        if (param.getApplicationFormId() != null) {
            formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        } else {
            formDetailBO = formManage.getFormDetailByCode(param.getApplicationFormCode());
        }
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO leaveFormDO = formDetailBO.getFormDO();
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(leaveFormDO.getUserId())) {
            log.info("outOfOfficeRevokeAdd | userInfo is on old attendance, userCode:{}" + leaveFormDO.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        // mex特殊校验
        commonFormOperationService.checkMexUser(leaveFormDO.getUserId(), ErrorCodeEnum.WAREHOUSE_NOT_ALLOW_OUT_OF_WORK, EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        //防止被重复撤销
        commonFormOperationService.repeatRevokeCheck(leaveFormDO.getId());
        AttendanceFormAttrDO outOfOfficeStartDate = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
        if (outOfOfficeStartDate == null) {
            throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(outOfOfficeStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        // 考勤周期校验
        attendanceCycleConfigService.checkDateInUserAttendanceCycle(leaveFormDO.getUserId(), startDate);
        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOS = new ArrayList<>();

        // 【外勤申请通过】外勤销假：重新生成一条外勤销假单，创建外勤销假单与外勤申请单的关联。所以不需要操作假期详情数据列表
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Lists.newArrayList();
        commonFormOperationService.commonRevokeDataAddBuild(param, formDetailBO, attendanceFormDO, formRelationDOS, formAttrDOList,
                idWorkUtils.nextNo(ApprovalNoPrefixEnum.OUT_OF_OFFICE_REVOKE), FormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode(), FormStatusEnum.IN_REVIEW.getCode());

        // api参数封装
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.outOfOfficeRevokeAddApprovalDataBuild(initInfoApiDTO, leaveFormDO.getApprovalId(), attendanceFormDO, formAttrDOList);
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        attendanceFormDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 落库
        attendanceApprovalManage.formAdd(attendanceFormDO, formRelationDOS, formAttrDOList,
                null, null, userLeaveStageDetailList, null);
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 外勤申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> outOfOfficePreview(OutOfOfficeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        //暂存就是保存前一步，必填数据都要填写完毕才可以
        commonFormOperationService.userBaseInfoBuild(null, param, null);
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = commonFormOperationService.userAbnormalRecordCheck(param.getAbnormalId());
        this.outOfOfficeAddDataCheck(param);
        //暂存不校验任何信息，直接落库成功，提交时校验
        AttendanceFormDO attendanceFormDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOList = new ArrayList<>();
        EmployeeAbnormalOperationRecordDO EmployeeAbnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        //参数构建，不落库
        outOfOfficeDataAddBuild(param, attendanceFormDO, formRelationDOList, formAttrDOList, EmployeeAbnormalOperationRecordDO, abnormalAttendanceDO);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.outOfOfficeAddApprovalDataBuild(initInfoApiDTO, attendanceFormDO, formAttrDOList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    /**
     * 外勤-撤销申请预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> outOfOfficeRevokePreview(RevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        AttendanceFormDetailBO formDetailBO = formManage.getFormDetailById(param.getApplicationFormId());
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO leaveFormDO = formDetailBO.getFormDO();
        List<AttendanceFormAttrDO> leaveAttrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = leaveAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        //防止被重复撤销
        commonFormOperationService.repeatRevokeCheck(leaveFormDO.getId());
        AttendanceFormAttrDO outOfOfficeStartDate = attrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
        if (outOfOfficeStartDate == null) {
            throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getDesc()));
        }
        Date startDate = DateUtil.parse(outOfOfficeStartDate.getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        // 考勤周期校验
        attendanceCycleConfigService.checkDateInUserAttendanceCycle(leaveFormDO.getUserId(), startDate);

        AttendanceFormDO formDO = new AttendanceFormDO();
        List<AttendanceFormAttrDO> formAttrDOList = new ArrayList<>();
        List<AttendanceFormRelationDO> formRelationDOS = new ArrayList<>();
        commonFormOperationService.commonRevokeDataAddBuild(param, formDetailBO, formDO, formRelationDOS, formAttrDOList,
                idWorkUtils.nextNo(ApprovalNoPrefixEnum.OUT_OF_OFFICE_REVOKE), FormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode(), FormStatusEnum.IN_REVIEW.getCode());

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.outOfOfficeAddApprovalDataBuild(initInfoApiDTO, formDO, formAttrDOList);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, leaveFormDO.getUserCode());
        return resultDTOList;
    }

    /**
     * 外勤申请业务校验
     *
     * @param param
     */
    private void outOfOfficeAddDataCheck(OutOfOfficeAddParam param) {
        if (param.getOutOfOfficeStartDate() == null) {
            throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_START_DATE_NOT_EMPTY.getDesc()));
        }
        if (param.getOutOfOfficeEndDate() == null) {
            throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_END_DATE_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_END_DATE_NOT_EMPTY.getDesc()));
        }
        if (StringUtils.isEmpty(param.getRemark())) {
            throw BusinessException.get(ErrorCodeEnum.REMARK_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.REMARK_NOT_EMPTY.getDesc()));
        }

        // 考勤周期 校验 查询考勤周期是否超过外勤时间
        attendanceCycleConfigService.checkDateInUserAttendanceCycle(param.getUserId(), param.getOutOfOfficeStartDate());
        //判断是否还有冲突
        List<ClashApplicationInfoDTO> clashApplicationInfoDTOList = new ArrayList<>();
        commonFormOperationService.selectClashApplication(param.getUserId(), param.getOutOfOfficeStartDate(), param.getOutOfOfficeEndDate(), clashApplicationInfoDTOList);
        if (CollectionUtils.isNotEmpty(clashApplicationInfoDTOList)) {
            throw BusinessException.get(ErrorCodeEnum.EXIST_CLASH_TIME_PERIOD.getCode(), I18nUtils.getMessage(ErrorCodeEnum.EXIST_CLASH_TIME_PERIOD.getDesc()));
        }
        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        commonFormOperationService.dayDurationInfoHandler(param.getUserId(),
                param.getOutOfOfficeStartDate(), param.getOutOfOfficeEndDate(),
                null, dayDurationInfoDTOList);
        //过滤不占用请假时间的日期
        dayDurationInfoDTOList = dayDurationInfoDTOList.stream()
                .filter(item -> item.getDays().compareTo(BigDecimal.ZERO) > 0
                        || item.getHours().compareTo(BigDecimal.ZERO) > 0
                        || item.getMinutes().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
        //校验外勤时长不超过7天
        checkOutOfOfficeTotalTime(dayDurationInfoDTOList);
        param.setDayDurationInfoDTOList(dayDurationInfoDTOList);
    }

    /**
     * 校验外勤时长不超过7天
     *
     * @param dayDurationInfoList
     */
    private void checkOutOfOfficeTotalTime(List<DayDurationInfoDTO> dayDurationInfoList) {
        if (CollectionUtils.isEmpty(dayDurationInfoList)) {
            throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_TIME_NOT_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_TIME_NOT_EMPTY.getDesc()));
        }
        BigDecimal totalDays = dayDurationInfoList.stream().map(item -> item.getDays()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //判断时长超过7天则报错
        if (totalDays.compareTo(BigDecimal.valueOf(7)) > 0) {
            throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_DAYS_EXCEED_7.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_DAYS_EXCEED_7.getDesc()));
        }
        if (totalDays.compareTo(BigDecimal.valueOf(7)) == 0) {
            BigDecimal totalHours = dayDurationInfoList.stream().map(item -> item.getHours()).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalMinutes = dayDurationInfoList.stream().map(item -> item.getMinutes()).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalHours.compareTo(BigDecimal.ZERO) > 0 || totalMinutes.compareTo(BigDecimal.ZERO) > 0) {
                throw BusinessException.get(ErrorCodeEnum.OUT_OF_OFFICE_DAYS_EXCEED_7.getCode(), I18nUtils.getMessage(ErrorCodeEnum.OUT_OF_OFFICE_DAYS_EXCEED_7.getDesc()));
            }
        }
    }

    private void outOfOfficeDataAddBuild(OutOfOfficeAddParam param,
                                         AttendanceFormDO formDO,
                                         List<AttendanceFormRelationDO> formRelationDOList,
                                         List<AttendanceFormAttrDO> formAttrDOList,
                                         EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO,
                                         EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {

        // 通用参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.OUT_OF_OFFICE.getCode());
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.OUT_OF_OFFICE));
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(formDO);
        // 构建表单详情
        this.setOutOfficeAttrInfo(param, formDO, formAttrDOList, formRelationDOList,
                employeeAbnormalOperationRecordDO, abnormalAttendanceDO);
    }

    private void outOfOfficeDataUpdateBuild(OutOfOfficeAddParam param,
                                            AttendanceFormDO formDO,
                                            List<AttendanceFormRelationDO> formRelationDOList,
                                            List<AttendanceFormAttrDO> formAttrDOList,
                                            EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO,
                                            EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        // 通用参数构建
        commonFormOperationService.commonFormAdd(param, formDO, FormTypeEnum.OUT_OF_OFFICE.getCode());
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(formDO);
        // 构建表单详情
        this.setOutOfficeAttrInfo(param, formDO, formAttrDOList, formRelationDOList,
                employeeAbnormalOperationRecordDO, abnormalAttendanceDO);
    }

    private void outOfOfficeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                                 AttendanceFormDO formDO,
                                                 List<AttendanceFormAttrDO> formAttrDOList) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        //审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);
        // 外勤单据信息
        this.setOutOfficeField(fieldApiDTOList, formAttrDOList);
        // 外勤时长（JSON）
        this.setDurationField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("outOfOfficeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));
    }

    private void outOfOfficeRevokeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                                       Long outOfOfficeApprovalId,
                                                       AttendanceFormDO formDO,
                                                       List<AttendanceFormAttrDO> formAttrDOList) {
        // 实体构建
        commonFormOperationService.commonApprovalAdd(initInfoApiDTO, formDO);

        // 关联外勤单据的审批ID
        if (outOfOfficeApprovalId != null) {
            initInfoApiDTO.setRelationApprovalIdList(Arrays.asList(outOfOfficeApprovalId));
        }

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        // 审批单基础信息
        this.setBaseField(fieldApiDTOList, formDO);
        // 外勤单据信息
        this.setOutOfficeField(fieldApiDTOList, formAttrDOList);

        //撤销原因
        List<AttendanceFormAttrDO> revokeReason = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.revokeReason.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REVOKE_REASON.getCode(), CollectionUtils.isNotEmpty(revokeReason) ? revokeReason.get(0).getAttrValue() : null, null);

        // 外勤时长（JSON）
        this.setDurationField(fieldApiDTOList, formAttrDOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);

        log.info("outOfOfficeRevokeAddApprovalDataBuild||调用BPM出参值为:{}", JSON.toJSONString(initInfoApiDTO));

    }

    private void setBaseField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                              AttendanceFormDO formDO) {
        //被审批人ID
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), formDO.getUserId() != null ? formDO.getUserId().toString() : null, null);

        //被申请人部门ID
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DEPT_ID.getCode(), formDO.getDeptId() != null ? formDO.getDeptId().toString() : null, null);

        //被申请人所在国
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_COUNTRY.getCode(), formDO.getCountry(), null);

        //被申请人结算国
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_ORIGIN_COUNTRY.getCode(), formDO.getOriginCountry(), null);

//        //被申请人是否仓内
//        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.IS_WAREHOUSE_STAFF.getCode(), formDO.getIsWarehouseStaff() != null ? formDO.getIsWarehouseStaff().toString() : null, null);

        //被申请人姓名
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_NAME.getCode(), formDO.getUserName(), null);

        //被申请人编码
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.USER_CODE.getCode(), formDO.getUserCode(), null);

        //被申请人部门
        AttendanceDept deptInfo = deptService.getByDeptId(formDO.getDeptId());
        if (deptInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
        }
        Map<String, String> deptMap = new HashMap<>();
        deptMap.put(LanguageTypeEnum.zh_CN.getCode(), deptInfo.getDeptNameCn());
        deptMap.put(LanguageTypeEnum.en_US.getCode(), deptInfo.getDeptNameEn());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DEPT_NAME.getCode(), deptInfo.getDeptNameEn(), deptMap);

        // 设置岗位
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());
        // 仓内外包人员不存在岗位,所以针对仓内外包人员不设置岗位
        if (!Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(formDO.getCountry())
                || ObjectUtil.notEqual(formDO.getIsWarehouseStaff(), 1)
                || ObjectUtil.notEqual(userInfo.getEmployeeType(), EmploymentTypeEnum.OS_FIXED_SALARY.getCode())) {
            //被申请人岗位
            AttendancePost postInfo = postService.getByPostId(formDO.getPostId());
            if (postInfo == null) {
                throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.POST_NAME.getCode(), postInfo.getPostNameEn(), null);
        }
    }

    private void setOutOfficeField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                   List<AttendanceFormAttrDO> formAttrDOList) {
        //外勤开始时间
        List<AttendanceFormAttrDO> outOfOfficeStartDateDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfOfficeStartDateDOList)) {
            Date outOfOfficeStartDate = DateUtil.parse(outOfOfficeStartDateDOList.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
            String outOfOfficeStartDateString = DateUtil.format(outOfOfficeStartDate, "yyyy-MM-dd HH:mm");
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.OUT_OF_OFFICE_START_DATE.getCode(), outOfOfficeStartDateString, null);
        }

        //外勤结束时间
        List<AttendanceFormAttrDO> outOfOfficeEndDateDOList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(outOfOfficeEndDateDOList)) {
            Date outOfOfficeEndDate = DateUtil.parse(outOfOfficeEndDateDOList.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
            String outOfOfficeEndDateString = DateUtil.format(outOfOfficeEndDate, "yyyy-MM-dd HH:mm");
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.OUT_OF_OFFICE_END_DATE.getCode(), outOfOfficeEndDateString, null);
        }

        //备注
        List<AttendanceFormAttrDO> remark = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.remark.getLowerCode())).collect(Collectors.toList());
        commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REMARK.getCode(), CollectionUtils.isNotEmpty(remark) ? remark.get(0).getAttrValue() : null, null);

        //附件
        List<AttendanceFormAttrDO> attachment = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachment)) {
            List<AttachmentDTO> attachmentList = JSON.parseArray(attachment.get(0).getAttrValue(), AttachmentDTO.class);
            List<FileTemplateApiDTO> fileTemplateApiDTOList = new ArrayList<>();
            for (AttachmentDTO attachmentDTO : attachmentList) {
                FileTemplateApiDTO apiDTO = new FileTemplateApiDTO();
                apiDTO.setFileName(attachmentDTO.getAttachmentName());
                apiDTO.setFileType(attachmentDTO.getAttachmentType());
                apiDTO.setFileUrl(attachmentDTO.getUrlPath());
                fileTemplateApiDTOList.add(apiDTO);
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.ATTACHMENT.getCode(), JSON.toJSONString(fileTemplateApiDTOList), null);
        }
    }

    private void setDurationField(List<ApprovalTypeFieldApiDTO> fieldApiDTOList,
                                  List<AttendanceFormAttrDO> formAttrDOList) {
        List<AttendanceFormAttrDO> dayInfoList = formAttrDOList.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode())).collect(Collectors.toList());
        List<String> dayIdList = new ArrayList<>();
        List<String> daysList = new ArrayList<>();
        List<String> hoursList = new ArrayList<>();
        List<String> minutesList = new ArrayList<>();
        List<String> legalWorkingHoursList = new ArrayList<>();
        List<String> dayShiftInfoList = new ArrayList<>();
        List<String> leaveInfoList = new ArrayList<>();
        List<String> restInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayInfoList)) {
            List<DayDurationInfoDTO> dayDurationInfoDTOList = JSON.parseArray(dayInfoList.get(0).getAttrValue(), DayDurationInfoDTO.class);

            BigDecimal days = BigDecimal.ZERO;
            BigDecimal hours = BigDecimal.ZERO;
            BigDecimal minutes = BigDecimal.ZERO;

            for (DayDurationInfoDTO dayDurationInfoDTO : dayDurationInfoDTOList) {
                days = days.add(dayDurationInfoDTO.getDays());
                hours = hours.add(dayDurationInfoDTO.getHours());
                minutes = minutes.add(dayDurationInfoDTO.getMinutes());
                dayIdList.add(dayDurationInfoDTO.getDayId().toString());
                daysList.add(dayDurationInfoDTO.getDays().toString());
                hoursList.add(dayDurationInfoDTO.getHours().toString());
                minutesList.add(dayDurationInfoDTO.getMinutes().toString());
                legalWorkingHoursList.add(dayDurationInfoDTO.getLegalWorkingHours().toString());
                dayShiftInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getDayShiftInfoList()));
                leaveInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getLeaveInfoList()));
                restInfoList.add(JSON.toJSONString(dayDurationInfoDTO.getRestInfoList()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAYS_LIST.getCode(), JSON.toJSONString(daysList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.HOURS_LIST.getCode(), JSON.toJSONString(hoursList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.MINUTES_LIST.getCode(), JSON.toJSONString(minutesList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.LEGAL_WORKING_HOURS_LIST.getCode(), JSON.toJSONString(legalWorkingHoursList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.DAY_SHIFT_INFO_LIST.getCode(), JSON.toJSONString(dayShiftInfoList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.LEAVE_INFO_LIST.getCode(), JSON.toJSONString(leaveInfoList), null);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.REST_INFO_LIST.getCode(), JSON.toJSONString(restInfoList), null);

            String descCN = days + "天" + hours + "小时" + minutes + "分钟";
            String descEN = days + "days" + hours + "hours" + minutes + "minutes";
            Map<String, String> expectedLeaveTimeMap = new HashMap<>();
            expectedLeaveTimeMap.put(LanguageTypeEnum.zh_CN.getCode(), descCN);
            expectedLeaveTimeMap.put(LanguageTypeEnum.en_US.getCode(), descEN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OutOfOfficeCustomFieldEnum.EXPECTED_OUT_OF_OFFICE_TIME.getCode(), descEN, expectedLeaveTimeMap);
        }
    }

    private void setOutOfficeAttrInfo(OutOfOfficeAddParam param,
                                      AttendanceFormDO formDO,
                                      List<AttendanceFormAttrDO> formAttrDOList,
                                      List<AttendanceFormRelationDO> formRelationDOList,
                                      EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO,
                                      EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        if (param.getOutOfOfficeStartDate() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode(), DateUtil.format(param.getOutOfOfficeStartDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (param.getOutOfOfficeEndDate() != null) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode(), DateUtil.format(param.getOutOfOfficeEndDate(), "yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(param.getRemark())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.remark.getLowerCode(), param.getRemark()));
        }
        if (CollectionUtils.isNotEmpty(param.getAttachmentList())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.attachmentList.getLowerCode(), JSON.toJSONString(param.getAttachmentList())));
        }
        if (CollectionUtils.isNotEmpty(param.getDayDurationInfoDTOList())) {
            formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.dayDurationInfoDTOList.getLowerCode(), JSON.toJSONString(param.getDayDurationInfoDTOList())));
        }

        //默认是没有被撤销
        formAttrDOList.add(commonFormOperationService.insertAttrDOBuild(formDO.getId(), ApplicationFormAttrKeyEnum.isRevoke.getLowerCode(), BusinessConstant.N.toString()));

        //把异常ID也关联下
        if (param.getAbnormalId() != null) {
            //把异常ID也关联下
            commonFormOperationService.associateAbnormal(formDO, formRelationDOList,
                    employeeAbnormalOperationRecordDO, param.getAbnormalId());
            employeeAbnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.OUT_OF_OFFICE.getCode());
            BaseDOUtil.fillDOInsert(formDO);
        }

        if (abnormalAttendanceDO != null) {
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
        }
    }

}
